<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useForm, useField } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useUserStore } from '@/stores/user'
import { useFederationsStore, type AgeDivision, type StyleDivision } from '@/stores/federations'
import { MatchType, CompetitionLevel, TypesOfGoals, EU_COUNTRIES, type AgendaEntry } from '@/stores/matches'
import { storeToRefs } from 'pinia'
import { api } from '@/api/feathers-client'
import type { MatchData } from '@/api/feathers-client'

// Form data interface
interface CreateMatchFormData {
  name: string
  isActive: boolean
  startDate?: string
  endDate?: string
  coverImageUrl?: string
  description?: string
  matchType?: string
  federationId?: number
  address?: string
  city?: string
  country?: string
  postcode?: string
  latitude?: number
  longitude?: number
  phone?: string
  email?: string
  judges?: unknown
  agenda?: unknown
  licenseRequired?: boolean
  currency?: string
  competitionLevel?: string
  international?: boolean
  withoutLimits?: boolean
  publishAt?: string
  registrationEnds?: string
  maxPlayersAmount?: number
  organizerId?: number
  ageDivisions?: unknown
  styleDivisions?: unknown
  payments?: unknown
}
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

// Configure dayjs plugins
dayjs.extend(utc)
dayjs.extend(timezone)

// UI Components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { DatePicker, DateTimePicker } from '@/components/ui/date-picker'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import LocationPickerMap from '@/components/map/LocationPickerMap.vue'
import { Plus, Trash2, ChevronLeft, ChevronRight, ChevronDown, ChevronUp, Save } from 'lucide-vue-next'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const federationsStore = useFederationsStore()

const { activeOrganizer } = storeToRefs(userStore)
const { federations } = storeToRefs(federationsStore)

// Validation schema
const validationSchema = toTypedSchema(
  z.object({
    // Step 1: Basic Info
    name: z.string().min(1, t('validation.required')),
    startDate: z.string().min(1, t('validation.required')),
    endDate: z.string().optional(),
    description: z.string().optional(),
    matchType: z.string().optional(),
    federationId: z.number().optional(),
    coverImageUrl: z.string().url().optional().or(z.literal('')),

    // Step 2: Location
    address: z.string().optional(),
    city: z.string().optional(),
    country: z.string().optional(),
    postcode: z.string().optional(),
    latitude: z.number().optional(),
    longitude: z.number().optional(),

    // Step 5: Details
    phone: z.string().optional(),
    email: z.string().email().optional().or(z.literal('')),

    // Step 7: Settings
    currency: z.string().optional(),
    competitionLevel: z.string().optional(),
    maxPlayersAmount: z.number().min(1).optional(),
    publishAt: z.string().optional(),
    registrationEnds: z.string().optional(),
    licenseRequired: z.boolean().optional(),
    international: z.boolean().optional(),
    withoutLimits: z.boolean().optional(),
  })
)

// Form validation
const { errors, validate, validateField } = useForm({
  validationSchema,
  initialValues: {
    name: '',
    isActive: true,
    startDate: '',
    endDate: '',
    description: '',
    matchType: '',
    federationId: undefined,
    coverImageUrl: '',
    address: '',
    city: '',
    country: '',
    postcode: '',
    latitude: undefined,
    longitude: undefined,
    phone: '',
    email: '',
    currency: 'PLN',
    competitionLevel: '',
    international: false,
    withoutLimits: false,
    licenseRequired: false,
    publishAt: '',
    registrationEnds: '',
    maxPlayersAmount: undefined,
  }
})

// Individual field validations
const { value: nameValue, errorMessage: nameError } = useField('name')
const { value: startDateValue, errorMessage: startDateError } = useField('startDate')
const { value: endDateValue, errorMessage: endDateError } = useField('endDate')
const { value: emailValue, errorMessage: emailError } = useField('email')
const { value: coverImageUrlValue, errorMessage: coverImageUrlError } = useField('coverImageUrl')
const { value: maxPlayersAmountValue, errorMessage: maxPlayersAmountError } = useField('maxPlayersAmount')

// Error count per step
const stepErrors = computed(() => {
  const errorCounts = [0, 0, 0, 0, 0, 0, 0] // 7 steps

  // Step 1: Basic Info
  if (nameError.value) errorCounts[0]++
  if (startDateError.value) errorCounts[0]++
  if (coverImageUrlError.value) errorCounts[0]++

  // Step 2: Location - no required fields

  // Step 3: Divisions - no validation needed

  // Step 4: Pricing - no validation needed

  // Step 5: Details
  if (emailError.value) errorCounts[4]++

  // Step 6: Agenda - no validation needed

  // Step 7: Settings
  if (maxPlayersAmountError.value) errorCounts[6]++

  return errorCounts
})

// Form state
const currentStep = ref(1)
const totalSteps = 7
const isSubmitting = ref(false)
const error = ref<string | null>(null)

// Form data
const formData = ref<CreateMatchFormData>({
  name: '',
  isActive: true,
  startDate: '',
  endDate: '',
  coverImageUrl: '',
  description: '',
  matchType: '',
  federationId: undefined,
  address: '',
  city: '',
  country: '',
  postcode: '',
  latitude: undefined,
  longitude: undefined,
  phone: '',
  email: '',
  judges: [],
  agenda: [],
  licenseRequired: false,
  currency: 'PLN',
  competitionLevel: '',
  international: false,
  withoutLimits: false,
  publishAt: '',
  registrationEnds: '',
  maxPlayersAmount: undefined,
  organizerId: undefined,
  ageDivisions: undefined,
  styleDivisions: undefined
})

// Divisions state
const ageDivisions = ref<AgeDivision[] | string[]>([])
const styleDivisions = ref<StyleDivision[] | string[]>([])
const isFederationBased = ref(false)

// Pricing state
const payments = ref<Record<string, number>>({})

// Draft state
const isDraftSaved = ref(false)
const lastSavedAt = ref<Date | null>(null)
const autoSaveEnabled = ref(true)
const autoSaveInterval = ref<NodeJS.Timeout | null>(null)
const DRAFT_KEY = 'create-match-draft'

// Judges and agenda items
const judges = ref<string[]>([])
const agendaItems = ref<AgendaEntry[]>([])
const expandedAgendaItems = ref<Set<number>>(new Set())

// Helper functions for judges
function addJudge() {
  judges.value.push('')
}

function removeJudge(index: number) {
  judges.value.splice(index, 1)
}

// Helper functions for agenda expansion
function toggleAgendaItem(index: number) {
  if (expandedAgendaItems.value.has(index)) {
    expandedAgendaItems.value.delete(index)
  } else {
    expandedAgendaItems.value.add(index)
  }
}

function isAgendaItemExpanded(index: number) {
  return expandedAgendaItems.value.has(index)
}

// Helper functions for divisions
function addAgeDivision() {
  if (isFederationBased.value) {
    // For federation-based, we work with string arrays
    (ageDivisions.value as string[]).push('')
  } else {
    // For custom, we work with AgeDivision objects
    (ageDivisions.value as AgeDivision[]).push({
      id: Date.now().toString(),
      name: '',
      short_name: '',
      min: undefined,
      max: undefined,
      isOpen: false
    })
  }
}

function removeAgeDivision(index: number) {
  ageDivisions.value.splice(index, 1)
}

function addStyleDivision() {
  if (isFederationBased.value) {
    // For federation-based, we work with string arrays
    (styleDivisions.value as string[]).push('')
  } else {
    // For custom, we work with StyleDivision objects
    (styleDivisions.value as StyleDivision[]).push({
      id: Date.now().toString(),
      name: '',
      short_name: ''
    })
  }
}

function removeStyleDivision(index: number) {
  styleDivisions.value.splice(index, 1)
}

// Helper functions for federation-based division selection
function toggleAgeDivision(shortName: string) {
  const currentDivisions = ageDivisions.value as string[]
  const index = currentDivisions.indexOf(shortName)

  if (index > -1) {
    // Remove if already selected
    currentDivisions.splice(index, 1)
  } else {
    // Add if not selected
    currentDivisions.push(shortName)
  }
}

function toggleStyleDivision(shortName: string) {
  const currentDivisions = styleDivisions.value as string[]
  const index = currentDivisions.indexOf(shortName)

  if (index > -1) {
    // Remove if already selected
    currentDivisions.splice(index, 1)
  } else {
    // Add if not selected
    currentDivisions.push(shortName)
  }
}

function isAgeDivisionSelected(shortName: string): boolean {
  return (ageDivisions.value as string[]).includes(shortName)
}

function isStyleDivisionSelected(shortName: string): boolean {
  return (styleDivisions.value as string[]).includes(shortName)
}

// Get available federation divisions
const selectedFederation = computed(() => {
  if (!formData.value.federationId) return null
  return federations.value.find(f => f.id === formData.value.federationId)
})

const availableFederationAgeDivisions = computed(() => {
  return selectedFederation.value?.ageDivisions || []
})

const availableFederationStyleDivisions = computed(() => {
  return selectedFederation.value?.styleDivisions || []
})

// Draft management functions
function saveDraft() {
  const draftData = {
    formData: formData.value,
    ageDivisions: ageDivisions.value,
    styleDivisions: styleDivisions.value,
    payments: payments.value,
    judges: judges.value,
    agendaItems: agendaItems.value,
    currentStep: currentStep.value,
    savedAt: new Date().toISOString()
  }

  localStorage.setItem(DRAFT_KEY, JSON.stringify(draftData))
  isDraftSaved.value = true
  lastSavedAt.value = new Date()
}

function loadDraft() {
  const draftJson = localStorage.getItem(DRAFT_KEY)
  if (!draftJson) return false

  try {
    const draftData = JSON.parse(draftJson)

    // Restore form data
    Object.assign(formData.value, draftData.formData)
    ageDivisions.value = draftData.ageDivisions || []
    styleDivisions.value = draftData.styleDivisions || []
    payments.value = draftData.payments || {}
    judges.value = draftData.judges || ['']
    agendaItems.value = draftData.agendaItems || []
    currentStep.value = draftData.currentStep || 1

    // Update validated fields
    nameValue.value = draftData.formData.name || ''
    startDateValue.value = draftData.formData.startDate || ''
    endDateValue.value = draftData.formData.endDate || ''
    emailValue.value = draftData.formData.email || ''
    coverImageUrlValue.value = draftData.formData.coverImageUrl || ''
    maxPlayersAmountValue.value = draftData.formData.maxPlayersAmount || undefined

    isDraftSaved.value = true
    lastSavedAt.value = new Date(draftData.savedAt)
    return true
  } catch (err) {
    console.error('Failed to load draft:', err)
    return false
  }
}

function discardDraft() {
  localStorage.removeItem(DRAFT_KEY)
  isDraftSaved.value = false
  lastSavedAt.value = null

  // Reset form to initial state
  Object.assign(formData.value, {
    name: '',
    isActive: true,
    startDate: '',
    endDate: '',
    coverImageUrl: '',
    description: '',
    matchType: '',
    federationId: undefined,
    address: '',
    city: '',
    country: '',
    postcode: '',
    latitude: undefined,
    longitude: undefined,
    phone: '',
    email: '',
    judges: [],
    agenda: [],
    licenseRequired: false,
    currency: 'PLN',
    competitionLevel: '',
    international: false,
    withoutLimits: false,
    publishAt: '',
    registrationEnds: '',
    maxPlayersAmount: undefined,
    organizerId: undefined,
    ageDivisions: undefined,
    styleDivisions: undefined
  })

  ageDivisions.value = []
  styleDivisions.value = []
  payments.value = {}
  judges.value = ['']
  agendaItems.value = []
  currentStep.value = 1

  // Reset validated fields
  nameValue.value = ''
  startDateValue.value = ''
  endDateValue.value = ''
  emailValue.value = ''
  coverImageUrlValue.value = ''
  maxPlayersAmountValue.value = undefined
}

function toggleAutoSave() {
  autoSaveEnabled.value = !autoSaveEnabled.value

  if (autoSaveEnabled.value) {
    startAutoSave()
  } else {
    stopAutoSave()
  }
}

function startAutoSave() {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value)
  }

  autoSaveInterval.value = setInterval(() => {
    if (autoSaveEnabled.value) {
      saveDraft()
    }
  }, 10000) // 10 seconds
}

function stopAutoSave() {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value)
    autoSaveInterval.value = null
  }
}

// Check if draft exists
const hasDraft = computed(() => {
  return localStorage.getItem(DRAFT_KEY) !== null
})

// Lifecycle hooks
onMounted(() => {
  // Load draft if it exists
  if (hasDraft.value) {
    loadDraft()
  }

  // Start auto-save if enabled
  if (autoSaveEnabled.value) {
    startAutoSave()
  }
})

onUnmounted(() => {
  // Clean up auto-save interval
  stopAutoSave()
})

// Get age division short names for pricing
const ageDivisionShortNames = computed(() => {
  if (isFederationBased.value) {
    // For federation-based, ageDivisions contains short_name strings
    return ageDivisions.value as string[]
  } else {
    // For custom, ageDivisions contains AgeDivision objects
    return (ageDivisions.value as AgeDivision[])
      .filter(div => div.short_name)
      .map(div => div.short_name)
  }
})

// Initialize with one judge and one agenda item if needed
if (judges.value.length === 0) {
  judges.value.push('')
}

// Load federations on mount
federationsStore.getAllFederations()

// Set organizer ID when activeOrganizer changes
watch(activeOrganizer, (organizer) => {
  if (organizer) {
    formData.value.organizerId = organizer.id
  }
}, { immediate: true })

// Auto-set end date when start date changes
watch(startDateValue, (newStartDate) => {
  if (newStartDate) {
    // Always set end date to the same date as start date when start date changes
    endDateValue.value = newStartDate
    formData.value.endDate = newStartDate
  }
})

// Auto-adjust start date when end date is set before start date
watch(endDateValue, (newEndDate) => {
  if (newEndDate && startDateValue.value) {
    const startDate = dayjs(startDateValue.value)
    const endDate = dayjs(newEndDate)

    // If end date is before start date, move start date to the same date as end date
    if (endDate.isBefore(startDate, 'day')) {
      startDateValue.value = newEndDate
      formData.value.startDate = newEndDate
    }
  }

  // Update formData
  if (newEndDate) {
    formData.value.endDate = newEndDate
  }
})

// Watch federation changes to update divisions mode
watch(() => formData.value.federationId, (federationId) => {
  isFederationBased.value = !!federationId
  if (federationId) {
    // Reset divisions when switching to federation-based
    ageDivisions.value = []
    styleDivisions.value = []
  } else {
    // Reset divisions when switching to custom
    ageDivisions.value = []
    styleDivisions.value = []
  }
})

// Watch divisions changes to update form data
watch([ageDivisions, styleDivisions], ([newAgeDivisions, newStyleDivisions]) => {
  formData.value.ageDivisions = newAgeDivisions
  formData.value.styleDivisions = newStyleDivisions
}, { deep: true })

// Watch age divisions changes to update payments
watch(ageDivisionShortNames, (newShortNames) => {
  const newPayments: Record<string, number> = {}

  // Keep existing prices for divisions that still exist
  newShortNames.forEach(shortName => {
    newPayments[shortName] = payments.value[shortName] || 0
  })

  payments.value = newPayments
  formData.value.payments = payments.value
}, { deep: true, immediate: true })

// Agenda date validation
const agendaDateErrors = computed(() => {
  const errors: Record<number, string> = {}

  if (!startDateValue.value) return errors

  const startDate = dayjs(startDateValue.value)
  const endDate = endDateValue.value ? dayjs(endDateValue.value) : startDate

  agendaItems.value.forEach((item, index) => {
    if (item.date) {
      const agendaDate = dayjs(item.date)

      if (agendaDate.isBefore(startDate, 'day')) {
        errors[index] = t('validation.agendaDateBeforeStart')
      } else if (agendaDate.isAfter(endDate, 'day')) {
        errors[index] = t('validation.agendaDateAfterEnd')
      }
    }
  })

  return errors
})

const hasAgendaDateErrors = computed(() => Object.keys(agendaDateErrors.value).length > 0)

// Step validation
const isStepValid = computed(() => {
  switch (currentStep.value) {
    case 1: // Basic Info
      return !nameError.value && !startDateError.value && !coverImageUrlError.value && nameValue.value && startDateValue.value
    case 2: // Location
      return true // All location fields are optional
    case 3: // Divisions
      return true // Divisions are optional
    case 4: // Pricing
      return true // Pricing is optional
    case 5: // Details
      return !emailError.value // Email validation if provided
    case 6: // Agenda
      return !hasAgendaDateErrors.value // Agenda dates must be within match dates
    case 7: // Settings
      return !maxPlayersAmountError.value // Max players validation if provided
    default:
      return false
  }
})

const canProceed = computed(() => isStepValid.value)
const canGoBack = computed(() => currentStep.value > 1)
const isLastStep = computed(() => currentStep.value === totalSteps)

// Navigation
function nextStep() {
  if (canProceed.value && currentStep.value < totalSteps) {
    currentStep.value++
  }
}

function previousStep() {
  if (canGoBack.value) {
    currentStep.value--
  }
}

function goToStep(step: number) {
  if (step >= 1 && step <= totalSteps) {
    currentStep.value = step
  }
}

// Date handling is now handled by the DatePicker and DateTimePicker components

// Agenda management
function addAgendaItem() {
  const newIndex = agendaItems.value.length
  agendaItems.value.push({
    name: '',
    description: '',
    location: '',
    date: '',
    time: '',
    goals: '',
    format: '',
    punctation: [],
    typesOfGoals: '',
    numberOfArrows: '',
    competitionBranch: ''
  })
  // Automatically expand the new item
  expandedAgendaItems.value.add(newIndex)
}

function removeAgendaItem(index: number) {
  agendaItems.value.splice(index, 1)
  // Update expanded items indices
  const newExpanded = new Set<number>()
  expandedAgendaItems.value.forEach(expandedIndex => {
    if (expandedIndex < index) {
      newExpanded.add(expandedIndex)
    } else if (expandedIndex > index) {
      newExpanded.add(expandedIndex - 1)
    }
  })
  expandedAgendaItems.value = newExpanded
}

// Form submission
async function submitForm() {
  if (!activeOrganizer.value) {
    error.value = 'No active organizer found'
    return
  }

  // Validate the entire form
  const { valid } = await validate()
  if (!valid) {
    error.value = t('validation.formHasErrors')
    return
  }

  isSubmitting.value = true
  error.value = null

  try {
    // Prepare form data with validated values
    const matchData: MatchData = {
      ...formData.value,
      name: nameValue.value,
      startDate: startDateValue.value,
      endDate: endDateValue.value,
      email: emailValue.value,
      coverImageUrl: coverImageUrlValue.value,
      maxPlayersAmount: maxPlayersAmountValue.value,
      organizerId: activeOrganizer.value.id,
      judges: judges.value.filter(j => j.trim()),
      agenda: agendaItems.value.filter(item => item.name || item.description),
      payments: payments.value
    } as MatchData

    // Create the match
    await api.matches.create(matchData)

    // Clean up form and draft after successful creation
    discardDraft()

    // Show success message and redirect
    router.push({ name: 'matches' })
  } catch (err) {
    console.error('Failed to create match:', err)
    error.value = t('matches.matchCreationFailed')
  } finally {
    isSubmitting.value = false
  }
}

// Step titles
const stepTitles = computed(() => [
  t('matches.basicInfo'),
  t('matches.locationInfo'),
  t('matches.divisionsInfo'),
  t('matches.pricingInfo'),
  t('matches.detailsInfo'),
  t('matches.agendaInfo'),
  t('matches.settingsInfo')
])
</script>

<template>
  <div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">{{ t('matches.createMatch') }}</h1>
          <p class="text-muted-foreground mt-2">
            {{ t('matches.step') }} {{ currentStep }} {{ t('matches.of') }} {{ totalSteps }}: {{ stepTitles[currentStep - 1] }}
          </p>
        </div>

        <!-- Draft Controls -->
        <div class="flex items-center gap-2">
          <div v-if="lastSavedAt" class="text-sm text-muted-foreground">
            {{ t('matches.lastSaved') }}: {{ lastSavedAt.toLocaleTimeString() }}
          </div>

          <Button @click="saveDraft" variant="outline" size="sm">
            <Save class="w-4 h-4 mr-2" />
            {{ t('matches.saveDraft') }}
          </Button>

          <Button
            v-if="hasDraft"
            @click="discardDraft"
            variant="outline"
            size="sm"
            class="text-destructive hover:text-destructive"
          >
            <Trash2 class="w-4 h-4 mr-2" />
            {{ t('matches.discardDraft') }}
          </Button>

          <div class="flex items-center gap-2">
            <Switch
              :checked="autoSaveEnabled"
              @update:checked="toggleAutoSave"
            />
            <Label class="text-sm">{{ t('matches.autoSave') }}</Label>
          </div>
        </div>
      </div>
    </div>

    <!-- Progress indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-2 flex-wrap gap-2">
        <button
          v-for="(title, index) in stepTitles"
          :key="index"
          class="text-sm font-medium flex items-center gap-2 px-2 py-1 rounded-md transition-colors hover:bg-muted/50 cursor-pointer"
          :class="currentStep === index + 1 ? 'bg-muted' : ''"
          @click="goToStep(index + 1)"
        >
          <div class="flex items-center gap-1">
            <Badge
              :variant="currentStep > index + 1 ? 'default' : currentStep === index + 1 ? 'secondary' : 'outline'"
              class="pointer-events-none"
            >
              {{ index + 1 }}
            </Badge>
            <Badge
              v-if="stepErrors[index] > 0"
              variant="destructive"
              class="text-xs px-1 py-0 min-w-[1.25rem] h-5"
            >
              {{ stepErrors[index] }}
            </Badge>
          </div>
          <span :class="currentStep === index + 1 ? 'text-primary' : 'text-muted-foreground'">
            {{ title }}
          </span>
        </button>
      </div>
      <div class="w-full bg-muted rounded-full h-2">
        <div
          class="bg-primary h-2 rounded-full transition-all duration-300"
          :style="{ width: `${(currentStep / totalSteps) * 100}%` }"
        />
      </div>
    </div>

    <!-- Error Alert -->
    <Alert v-if="error" variant="destructive" class="mb-6">
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Form Steps -->
    <Card>
      <CardHeader>
        <CardTitle>{{ stepTitles[currentStep - 1] }}</CardTitle>
        <CardDescription v-if="currentStep === 1">
          {{ t('matches.basicInfo') }} - {{ t('common.required') }}
        </CardDescription>
        <CardDescription v-else>
          {{ stepTitles[currentStep - 1] }} - {{ t('matches.optional') }}
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 1" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">{{ t('matches.name') }} *</Label>
              <Input
                id="name"
                v-model="nameValue"
                :placeholder="t('matches.name')"
                :class="nameError ? 'border-destructive' : ''"
                required
              />
              <p v-if="nameError" class="text-sm text-destructive">{{ nameError }}</p>
            </div>
            <div class="space-y-2">
              <Label for="matchType">{{ t('matches.matchType') }}</Label>
              <Select
                :model-value="formData.matchType"
                @update:model-value="formData.matchType = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select match type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="MatchType.THREED">3D</SelectItem>
                  <SelectItem :value="MatchType.FIELD">Field</SelectItem>
                  <SelectItem :value="MatchType.OUTDOOR">Outdoor</SelectItem>
                  <SelectItem :value="MatchType.INDOOR">Indoor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="federation">{{ t('matches.federation') }}</Label>
              <Select
                :model-value="formData.federationId?.toString()"
                @update:model-value="formData.federationId = $event ? parseInt($event as string) : undefined"
              >
                <SelectTrigger>
                  <SelectValue :placeholder="t('matches.selectFederation')" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="federation in federations"
                    :key="federation.id"
                    :value="federation.id.toString()"
                  >
                    {{ federation.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="coverImageUrl">{{ t('matches.coverImageUrl') }}</Label>
              <Input
                id="coverImageUrl"
                v-model="coverImageUrlValue"
                :placeholder="t('matches.coverImageUrl')"
                type="url"
                :class="coverImageUrlError ? 'border-destructive' : ''"
              />
              <p v-if="coverImageUrlError" class="text-sm text-destructive">{{ coverImageUrlError }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="startDate">{{ t('matches.startDate') }} *</Label>
              <DatePicker
                v-model="startDateValue"
                placeholder="Select start date"
                :class="startDateError ? 'border-destructive' : ''"
              />
              <p v-if="startDateError" class="text-sm text-destructive">{{ startDateError }}</p>
            </div>
            <div class="space-y-2">
              <Label for="endDate">{{ t('matches.endDate') }}</Label>
              <DatePicker
                v-model="endDateValue"
                placeholder="Select end date"
                :class="endDateError ? 'border-destructive' : ''"
              />
              <p v-if="endDateError" class="text-sm text-destructive">{{ endDateError }}</p>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="description">{{ t('matches.description') }}</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              :placeholder="t('matches.description')"
              rows="4"
            />
          </div>
        </div>

        <!-- Step 2: Location -->
        <div v-if="currentStep === 2" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="address">{{ t('matches.address') }}</Label>
              <Input
                id="address"
                v-model="formData.address"
                :placeholder="t('matches.address')"
              />
            </div>
            <div class="space-y-2">
              <Label for="city">{{ t('matches.city') }}</Label>
              <Input
                id="city"
                v-model="formData.city"
                :placeholder="t('matches.city')"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="country">{{ t('matches.country') }}</Label>
              <Select
                :model-value="formData.country"
                @update:model-value="formData.country = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="country in EU_COUNTRIES"
                    :key="country.code"
                    :value="country.code"
                  >
                    {{ country.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="postcode">{{ t('matches.postcode') }}</Label>
              <Input
                id="postcode"
                v-model="formData.postcode"
                :placeholder="t('matches.postcode')"
              />
            </div>
          </div>

          <!-- Interactive Map -->
          <div class="space-y-2">
            <Label>Location on Map</Label>
            <LocationPickerMap
              :latitude="formData.latitude"
              :longitude="formData.longitude"
              :address="formData.address"
              :city="formData.city"
              :country="formData.country"
              :postcode="formData.postcode"
              @update:latitude="formData.latitude = $event || undefined"
              @update:longitude="formData.longitude = $event || undefined"
              @update:address="formData.address = $event"
              @update:city="formData.city = $event"
              @update:country="formData.country = $event"
              @update:postcode="formData.postcode = $event"
            />
          </div>

          <!-- Coordinate Fields -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="latitude">{{ t('matches.latitude') }}</Label>
              <Input
                id="latitude"
                :value="formData.latitude?.toString() || ''"
                type="number"
                step="any"
                placeholder="Latitude"
                @input="formData.latitude = parseFloat(($event.target as HTMLInputElement).value) || undefined"
              />
            </div>
            <div class="space-y-2">
              <Label for="longitude">{{ t('matches.longitude') }}</Label>
              <Input
                id="longitude"
                :value="formData.longitude?.toString() || ''"
                type="number"
                step="any"
                placeholder="Longitude"
                @input="formData.longitude = parseFloat(($event.target as HTMLInputElement).value) || undefined"
              />
            </div>
          </div>
        </div>

        <!-- Step 3: Divisions -->
        <div v-if="currentStep === 3" class="space-y-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Age Divisions -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <Label class="text-base font-semibold">{{ t('matches.ageDivisions') }}</Label>
                <Button
                  v-if="!isFederationBased"
                  @click="addAgeDivision"
                  variant="outline"
                  size="sm"
                >
                  <Plus class="w-4 h-4 mr-2" />
                  {{ t('matches.addAgeDivision') }}
                </Button>
              </div>

              <div v-if="ageDivisions.length === 0 && !isFederationBased" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
                <p class="text-muted-foreground">{{ t('matches.noAgeDivisions') }}</p>
                <Button @click="addAgeDivision" variant="outline" class="mt-2">
                  <Plus class="w-4 h-4 mr-2" />
                  {{ t('matches.addAgeDivision') }}
                </Button>
              </div>

              <div v-else class="space-y-3">
                <!-- Federation-based age divisions -->
                <template v-if="isFederationBased">
                  <div v-if="availableFederationAgeDivisions.length === 0" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
                    <p class="text-muted-foreground">{{ t('matches.noFederationAgeDivisions') }}</p>
                  </div>
                  <div v-else class="p-4 border rounded-lg">
                    <p class="text-sm text-muted-foreground mb-4">{{ t('matches.selectMultipleAgeDivisions') }}</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div
                        v-for="fedDivision in availableFederationAgeDivisions"
                        :key="fedDivision.short_name"
                        class="flex items-center space-x-2"
                      >
                        <Checkbox
                          :id="`age-div-${fedDivision.short_name}`"
                          :checked="isAgeDivisionSelected(fedDivision.short_name)"
                          @update:checked="toggleAgeDivision(fedDivision.short_name)"
                        />
                        <Label
                          :for="`age-div-${fedDivision.short_name}`"
                          class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {{ fedDivision.name }} ({{ fedDivision.short_name }})
                        </Label>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- Custom age divisions -->
                <template v-else>
                  <div
                    v-for="(division, index) in ageDivisions"
                    :key="index"
                    class="p-3 border rounded-lg space-y-3"
                  >
                  <div class="flex items-center justify-between">
                    <span class="font-medium">{{ t('matches.ageDivision') }} {{ index + 1 }}</span>
                    <Button
                      @click="removeAgeDivision(index)"
                      variant="outline"
                      size="sm"
                      class="text-destructive hover:text-destructive"
                    >
                      <Trash2 class="w-4 h-4" />
                    </Button>
                  </div>

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div class="space-y-1">
                      <Label :for="`age-short-name-${index}`">{{ t('matches.shortName') }} *</Label>
                      <Input
                        :id="`age-short-name-${index}`"
                        v-model="(ageDivisions as AgeDivision[])[index].short_name"
                        :placeholder="t('matches.shortName')"
                      />
                    </div>
                    <div class="space-y-1">
                      <Label :for="`age-name-${index}`">{{ t('matches.name') }}</Label>
                      <Input
                        :id="`age-name-${index}`"
                        v-model="(ageDivisions as AgeDivision[])[index].name"
                        :placeholder="t('matches.name')"
                      />
                    </div>
                  </div>

                  <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                    <div class="space-y-1">
                      <Label :for="`age-min-${index}`">{{ t('matches.minAge') }}</Label>
                      <Input
                        :id="`age-min-${index}`"
                        v-model="(ageDivisions as AgeDivision[])[index].min"
                        type="number"
                        min="0"
                        max="100"
                        :placeholder="t('matches.minAge')"
                      />
                    </div>
                    <div class="space-y-1">
                      <Label :for="`age-max-${index}`">{{ t('matches.maxAge') }}</Label>
                      <Input
                        :id="`age-max-${index}`"
                        v-model="(ageDivisions as AgeDivision[])[index].max"
                        type="number"
                        min="0"
                        max="100"
                        :placeholder="t('matches.maxAge')"
                      />
                    </div>
                    <div class="flex items-center space-x-2 pt-6">
                      <Checkbox
                        :id="`age-open-${index}`"
                        :checked="(ageDivisions as AgeDivision[])[index].isOpen"
                        @update:checked="(ageDivisions as AgeDivision[])[index].isOpen = $event"
                      />
                      <Label :for="`age-open-${index}`">{{ t('matches.isOpen') }}</Label>
                    </div>
                  </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- Style Divisions -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <Label class="text-base font-semibold">{{ t('matches.styleDivisions') }}</Label>
                <Button
                  v-if="!isFederationBased"
                  @click="addStyleDivision"
                  variant="outline"
                  size="sm"
                >
                  <Plus class="w-4 h-4 mr-2" />
                  {{ t('matches.addStyleDivision') }}
                </Button>
              </div>

              <div v-if="styleDivisions.length === 0 && !isFederationBased" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
                <p class="text-muted-foreground">{{ t('matches.noStyleDivisions') }}</p>
                <Button @click="addStyleDivision" variant="outline" class="mt-2">
                  <Plus class="w-4 h-4 mr-2" />
                  {{ t('matches.addStyleDivision') }}
                </Button>
              </div>

              <div v-else class="space-y-3">
                <!-- Federation-based style divisions -->
                <template v-if="isFederationBased">
                  <div v-if="availableFederationStyleDivisions.length === 0" class="text-center py-6 border-2 border-dashed border-muted rounded-lg">
                    <p class="text-muted-foreground">{{ t('matches.noFederationStyleDivisions') }}</p>
                  </div>
                  <div v-else class="p-4 border rounded-lg">
                    <p class="text-sm text-muted-foreground mb-4">{{ t('matches.selectMultipleStyleDivisions') }}</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div
                        v-for="fedDivision in availableFederationStyleDivisions"
                        :key="fedDivision.short_name"
                        class="flex items-center space-x-2"
                      >
                        <Checkbox
                          :id="`style-div-${fedDivision.short_name}`"
                          :checked="isStyleDivisionSelected(fedDivision.short_name)"
                          @update:checked="toggleStyleDivision(fedDivision.short_name)"
                        />
                        <Label
                          :for="`style-div-${fedDivision.short_name}`"
                          class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                        >
                          {{ fedDivision.name }} ({{ fedDivision.short_name }})
                        </Label>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- Custom style divisions -->
                <template v-else>
                  <div
                    v-for="(division, index) in styleDivisions"
                    :key="`custom-style-${index}`"
                    class="p-3 border rounded-lg space-y-3"
                  >
                  <div class="flex items-center justify-between">
                    <span class="font-medium">{{ t('matches.styleDivision') }} {{ index + 1 }}</span>
                    <Button
                      @click="removeStyleDivision(index)"
                      variant="outline"
                      size="sm"
                      class="text-destructive hover:text-destructive"
                    >
                      <Trash2 class="w-4 h-4" />
                    </Button>
                  </div>

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div class="space-y-1">
                      <Label :for="`style-short-name-${index}`">{{ t('matches.shortName') }} *</Label>
                      <Input
                        :id="`style-short-name-${index}`"
                        v-model="(styleDivisions as StyleDivision[])[index].short_name"
                        :placeholder="t('matches.shortName')"
                      />
                    </div>
                    <div class="space-y-1">
                      <Label :for="`style-name-${index}`">{{ t('matches.name') }} *</Label>
                      <Input
                        :id="`style-name-${index}`"
                        v-model="(styleDivisions as StyleDivision[])[index].name"
                        :placeholder="t('matches.name')"
                      />
                    </div>
                  </div>
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- Federation Info -->
          <div v-if="isFederationBased && selectedFederation" class="p-4 bg-muted/30 rounded-lg">
            <p class="text-sm text-muted-foreground">
              {{ t('matches.federationDivisionsInfo', { federation: selectedFederation.name }) }}
            </p>
          </div>
        </div>

        <!-- Step 4: Pricing -->
        <div v-if="currentStep === 4" class="space-y-6">
          <div v-if="ageDivisionShortNames.length === 0" class="text-center py-8">
            <p class="text-muted-foreground">{{ t('matches.noPricingAvailable') }}</p>
            <p class="text-sm text-muted-foreground mt-2">{{ t('matches.addAgeDivisionsFirst') }}</p>
          </div>

          <div v-else class="space-y-4">
            <div class="mb-4">
              <h3 class="text-lg font-semibold mb-2">{{ t('matches.pricingByAgeDivision') }}</h3>
              <p class="text-sm text-muted-foreground">{{ t('matches.pricingDescription') }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="shortName in ageDivisionShortNames"
                :key="shortName"
                class="p-4 border rounded-lg space-y-3"
              >
                <div class="flex items-center justify-between">
                  <Label class="font-medium">{{ shortName }}</Label>
                  <Badge variant="outline">{{ formData.currency || 'PLN' }}</Badge>
                </div>

                <div class="space-y-2">
                  <Label :for="`price-${shortName}`">{{ t('matches.entryFee') }}</Label>
                  <Input
                    :id="`price-${shortName}`"
                    v-model.number="payments[shortName]"
                    type="number"
                    min="0"
                    step="0.01"
                    :placeholder="t('matches.enterPrice')"
                    class="text-right"
                  />
                </div>
              </div>
            </div>

            <!-- Summary -->
            <div class="mt-6 p-4 bg-muted/30 rounded-lg">
              <h4 class="font-medium mb-2">{{ t('matches.pricingSummary') }}</h4>
              <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 text-sm">
                <div
                  v-for="shortName in ageDivisionShortNames"
                  :key="`summary-${shortName}`"
                  class="flex justify-between"
                >
                  <span>{{ shortName }}:</span>
                  <span class="font-medium">{{ payments[shortName] || 0 }} {{ formData.currency || 'PLN' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 5: Details -->
        <div v-if="currentStep === 5" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="phone">{{ t('matches.phone') }}</Label>
              <Input
                id="phone"
                v-model="formData.phone"
                :placeholder="t('matches.phone')"
                type="tel"
              />
            </div>
            <div class="space-y-2">
              <Label for="email">{{ t('matches.email') }}</Label>
              <Input
                id="email"
                v-model="emailValue"
                :placeholder="t('matches.email')"
                type="email"
                :class="emailError ? 'border-destructive' : ''"
              />
              <p v-if="emailError" class="text-sm text-destructive">{{ emailError }}</p>
            </div>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Label>{{ t('matches.judges') }}</Label>
              <Button @click="addJudge" variant="outline" size="sm">
                <Plus class="w-4 h-4 mr-2" />
                Add Judge
              </Button>
            </div>

            <div v-if="judges.length === 0" class="text-center py-4">
              <p class="text-muted-foreground">No judges added yet</p>
              <Button @click="addJudge" variant="outline" class="mt-2">
                <Plus class="w-4 h-4 mr-2" />
                Add Judge
              </Button>
            </div>

            <div v-else class="space-y-2">
              <div
                v-for="(judge, index) in judges"
                :key="index"
                class="flex items-center gap-2"
              >
                <Input
                  v-model="judges[index]"
                  :placeholder="`Judge ${index + 1} name`"
                  class="flex-1"
                />
                <Button
                  @click="removeJudge(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 6: Agenda -->
        <div v-if="currentStep === 6" class="space-y-4">
          <div class="flex items-center justify-between">
            <Label>{{ t('matches.agendaInfo') }}</Label>
            <Button @click="addAgendaItem" variant="outline" size="sm">
              <Plus class="w-4 h-4 mr-2" />
              {{ t('matches.addAgendaItem') }}
            </Button>
          </div>

          <div v-if="agendaItems.length === 0" class="text-center py-8">
            <p class="text-muted-foreground">No agenda items yet</p>
            <Button @click="addAgendaItem" variant="outline" class="mt-2">
              <Plus class="w-4 h-4 mr-2" />
              {{ t('matches.addAgendaItem') }}
            </Button>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(item, index) in agendaItems"
              :key="index"
              class="border rounded-lg overflow-hidden"
            >
              <!-- Collapsed Header -->
              <div
                class="p-4 bg-muted/30 cursor-pointer flex items-center justify-between hover:bg-muted/50 transition-colors"
                @click="toggleAgendaItem(index)"
              >
                <div class="flex items-center gap-3 flex-1 min-w-0">
                  <div class="flex items-center gap-2">
                    <component
                      :is="isAgendaItemExpanded(index) ? ChevronUp : ChevronDown"
                      class="w-4 h-4 text-muted-foreground"
                    />
                    <span class="font-medium">{{ item.name || `Agenda Item ${index + 1}` }}</span>
                  </div>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground truncate">
                    <span v-if="item.date">{{ item.date }}</span>
                    <span v-if="item.time">{{ item.time }}</span>
                    <span v-if="item.location" class="truncate">{{ item.location }}</span>
                  </div>
                </div>
                <Button
                  @click.stop="removeAgendaItem(index)"
                  variant="outline"
                  size="sm"
                  class="text-destructive hover:text-destructive ml-2"
                >
                  <Trash2 class="w-4 h-4" />
                </Button>
              </div>

              <!-- Expanded Content -->
              <div v-if="isAgendaItemExpanded(index)" class="p-4 space-y-3 border-t bg-background">

              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-name-${index}`">Name *</Label>
                  <Input
                    :id="`agenda-name-${index}`"
                    v-model="item.name"
                    placeholder="Event name"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-location-${index}`">Location</Label>
                  <Input
                    :id="`agenda-location-${index}`"
                    v-model="item.location"
                    placeholder="Event location"
                  />
                </div>
              </div>

              <div class="space-y-1">
                <Label :for="`agenda-description-${index}`">Description</Label>
                <Textarea
                  :id="`agenda-description-${index}`"
                  v-model="item.description"
                  placeholder="Event description"
                  rows="2"
                />
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-date-${index}`">Date</Label>
                  <DatePicker
                    v-model="item.date"
                    placeholder="Select date"
                    :class="agendaDateErrors[index] ? 'border-destructive' : ''"
                  />
                  <p v-if="agendaDateErrors[index]" class="text-sm text-destructive">
                    {{ agendaDateErrors[index] }}
                  </p>
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-time-${index}`">Time</Label>
                  <Input
                    :id="`agenda-time-${index}`"
                    v-model="item.time"
                    type="time"
                    placeholder="Event time"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-branch-${index}`">Competition Branch</Label>
                  <Select
                    :model-value="item.competitionBranch"
                    @update:model-value="item.competitionBranch = ($event as string) || undefined"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem :value="MatchType.THREED">3D</SelectItem>
                      <SelectItem :value="MatchType.FIELD">Field</SelectItem>
                      <SelectItem :value="MatchType.OUTDOOR">Outdoor</SelectItem>
                      <SelectItem :value="MatchType.INDOOR">Indoor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div class="space-y-1">
                  <Label :for="`agenda-goals-${index}`">Goals</Label>
                  <Input
                    :id="`agenda-goals-${index}`"
                    v-model="item.goals"
                    placeholder="Number of goals"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-arrows-${index}`">Number of Arrows</Label>
                  <Input
                    :id="`agenda-arrows-${index}`"
                    v-model="item.numberOfArrows"
                    placeholder="Number of arrows"
                  />
                </div>
                <div class="space-y-1">
                  <Label :for="`agenda-types-${index}`">Types of Goals</Label>
                  <Select
                    :model-value="item.typesOfGoals"
                    @update:model-value="item.typesOfGoals = ($event as string) || undefined"
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem :value="TypesOfGoals.THREED">3D</SelectItem>
                      <SelectItem :value="TypesOfGoals.PAPER">Paper</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 7: Settings -->
        <div v-if="currentStep === 7" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="currency">{{ t('matches.currency') }}</Label>
              <Select
                :model-value="formData.currency"
                @update:model-value="formData.currency = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PLN">PLN</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="CZK">CZK</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="space-y-2">
              <Label for="competitionLevel">{{ t('matches.competitionLevel') }}</Label>
              <Select
                :model-value="formData.competitionLevel"
                @update:model-value="formData.competitionLevel = ($event as string) || undefined"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select competition level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="CompetitionLevel.FRIENDLY">Friendly</SelectItem>
                  <SelectItem :value="CompetitionLevel.TOURNAMENT">Tournament</SelectItem>
                  <SelectItem :value="CompetitionLevel.NATIONAL">National</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="maxPlayersAmount">{{ t('matches.maxPlayersAmount') }}</Label>
              <Input
                id="maxPlayersAmount"
                v-model="maxPlayersAmountValue"
                type="number"
                min="1"
                :placeholder="t('matches.maxPlayersAmount')"
                :class="maxPlayersAmountError ? 'border-destructive' : ''"
              />
              <p v-if="maxPlayersAmountError" class="text-sm text-destructive">{{ maxPlayersAmountError }}</p>
            </div>
            <div class="space-y-2">
              <Label for="registrationEnds">{{ t('matches.registrationEnds') }}</Label>
              <DateTimePicker
                v-model="formData.registrationEnds"
                placeholder="Select registration end date and time"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="publishAt">{{ t('matches.publishAt') }}</Label>
            <DateTimePicker
              v-model="formData.publishAt"
              placeholder="Select publish date and time"
            />
          </div>

          <Separator />

          <div class="space-y-4">
            <div class="flex items-center space-x-2">
              <Checkbox
                id="licenseRequired"
                :checked="formData.licenseRequired"
                @update:checked="formData.licenseRequired = $event"
              />
              <Label for="licenseRequired">{{ t('matches.licenseRequired') }}</Label>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox
                id="international"
                :checked="formData.international"
                @update:checked="formData.international = $event"
              />
              <Label for="international">{{ t('matches.international') }}</Label>
            </div>

            <div class="flex items-center space-x-2">
              <Checkbox
                id="withoutLimits"
                :checked="formData.withoutLimits"
                @update:checked="formData.withoutLimits = $event"
              />
              <Label for="withoutLimits">{{ t('matches.withoutLimits') }}</Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Navigation -->
    <div class="flex justify-between mt-8">
      <Button
        @click="previousStep"
        variant="outline"
        :disabled="!canGoBack"
      >
        <ChevronLeft class="w-4 h-4 mr-2" />
        {{ t('common.previous') }}
      </Button>

      <div class="flex gap-2">
        <Button @click="router.push({ name: 'matches' })" variant="ghost">
          {{ t('common.cancel') }}
        </Button>

        <Button
          v-if="!isLastStep"
          @click="nextStep"
          :disabled="!canProceed"
        >
          {{ t('common.next') }}
          <ChevronRight class="w-4 h-4 ml-2" />
        </Button>

        <Button
          v-else
          @click="submitForm"
          :disabled="!canProceed || isSubmitting"
        >
          {{ isSubmitting ? t('common.loading') : t('common.save') }}
        </Button>
      </div>
    </div>
  </div>
</template>
